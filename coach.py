import os
import sqlite3
import json
import datetime
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
KNOWLEDGE_BASE_DIR = "knowledge_data"
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"


# --- SYSTEM INITIALIZATION ---

def setup_directories():
    """Create necessary directories if they don't exist."""
    os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs(CHROMA_DB_PATH, exist_ok=True)


def create_db_tables(conn):
    """Create the SQLite database tables based on our English design."""
    cursor = conn.cursor()

    # User_Profile: Stores the user's general state.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Profile (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            current_main_focus TEXT DEFAULT 'Enablers'
        )
    ''')

    # Skills: Tracks the user's skill level in different techniques.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Skills (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            skill_name TEXT UNIQUE NOT NULL,
            phase TEXT NOT NULL,
            level TEXT DEFAULT 'unknown',
            last_updated TEXT NOT NULL
        )
    ''')

    # Practice_Blocks: Logs practice sessions.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Practice_Blocks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            skill_id INTEGER,
            duration_minutes INTEGER,
            difficulty_notes TEXT,
            FOREIGN KEY (skill_id) REFERENCES Skills (id)
        )
    ''')

    # Distraction_Habits: Keeps an inventory of personal obstacles.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Distraction_Habits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            description TEXT NOT NULL,
            status TEXT DEFAULT 'active'
        )
    ''')

    conn.commit()
    print("Database tables checked/created successfully.")


def setup_database():
    """Initializes the SQLite database and its tables."""
    conn = sqlite3.connect(DB_PATH)
    create_db_tables(conn)

    # Check if user profile exists, if not, create one.
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM User_Profile WHERE id = 1")
    if cursor.fetchone() is None:
        print("No user profile found. Let's create one.")
        name = input("What is your name? ")
        cursor.execute(
            "INSERT INTO User_Profile (id, name) VALUES (?, ?)", (1, name))
        conn.commit()
        print(f"Welcome, {name}! Your profile has been created.")

    conn.close()


def load_knowledge_base_if_empty(collection):
    """Loads knowledge from JSON files into ChromaDB if the collection is empty."""
    if collection.count() > 0:
        print("Knowledge base is already loaded.")
        return

    print("Knowledge base is empty. Loading from JSON files...")
    documents = []
    metadatas = []
    ids = []

    for filename in os.listdir(KNOWLEDGE_BASE_DIR):
        if filename.endswith('.json'):
            filepath = os.path.join(KNOWLEDGE_BASE_DIR, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                concepts = json.load(f)
                for concept in concepts:
                    # The document for embedding is a rich combination of its parts
                    doc_content = (
                        f"Concept: {concept['concept_name']}. "
                        f"Summary: {concept['summary']}. "
                        f"Instructions: {' '.join(concept['instructions'])}. "
                        f"Context: {concept['additional_context']}"
                    )
                    documents.append(doc_content)
                    metadatas.append({
                        "concept_id": concept['concept_id'],
                        "concept_name": concept['concept_name'],
                        "ics_phase": concept['ics_phase']
                    })
                    ids.append(concept['concept_id'])

    if ids:
        collection.add(documents=documents, metadatas=metadatas, ids=ids)
        print(
            f"Successfully loaded {len(ids)} concepts into the knowledge base.")


def configure_gemini():
    """Configures the Gemini API."""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError(
            "GOOGLE_API_KEY not found in .env file. Please set it.")
    genai.configure(api_key=api_key)


def initialize_system():
    """Runs all setup functions to prepare the application."""
    print("Initializing The Coach...")
    setup_directories()
    setup_database()

    # Setup ChromaDB
    chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
    collection = chroma_client.get_or_create_collection(
        name=KNOWLEDGE_BASE_COLLECTION_NAME)
    load_knowledge_base_if_empty(collection)

    # Configure Gemini
    configure_gemini()

    print("Initialization complete. The Coach is ready.\n")
    return collection


# --- CORE COACH LOGIC ---

def query_knowledge_base(collection, query_text, n_results=3):
    """Queries the ChromaDB knowledge base for relevant concepts."""
    results = collection.query(query_texts=[query_text], n_results=n_results)
    return results['documents'][0] if results else []


def get_gemini_response(prompt):
    """Gets a response from the Gemini model."""
    model = genai.GenerativeModel('gemini-2.0-flash')
    response = model.generate_content(prompt)
    return response.text


def get_user_context():
    """Gets comprehensive user context from the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user profile
    cursor.execute(
        "SELECT name, current_main_focus FROM User_Profile WHERE id = 1")
    profile = cursor.fetchone()

    if not profile:
        conn.close()
        return "No user profile found."

    name, main_focus = profile

    # Get recent practice blocks
    cursor.execute("""
        SELECT pb.date, s.skill_name, pb.duration_minutes, pb.difficulty_notes
        FROM Practice_Blocks pb
        JOIN Skills s ON pb.skill_id = s.id
        ORDER BY pb.date DESC
        LIMIT 3
    """)
    recent_practice = cursor.fetchall()

    # Get current skill levels
    cursor.execute("""
        SELECT skill_name, level, phase
        FROM Skills
        WHERE level != 'unknown'
        ORDER BY last_updated DESC
        LIMIT 5
    """)
    skills = cursor.fetchall()

    # Get active distractions
    cursor.execute("""
        SELECT description, type
        FROM Distraction_Habits
        WHERE status = 'active'
        LIMIT 3
    """)
    distractions = cursor.fetchall()

    conn.close()

    # Build context string
    context = f"User: {name}\nCurrent Main Focus: {main_focus}\n"

    if skills:
        context += "\nCurrent Skills:\n"
        for skill, level, phase in skills:
            context += f"- {skill} ({phase}): {level}\n"

    if recent_practice:
        context += "\nRecent Practice Sessions:\n"
        for date, skill, duration, notes in recent_practice:
            context += f"- {date}: {skill} for {duration} minutes"
            if notes:
                context += f" (Notes: {notes})"
            context += "\n"

    if distractions:
        context += "\nActive Challenges:\n"
        for desc, dist_type in distractions:
            context += f"- {dist_type}: {desc}\n"

    return context


def manage_coach_response(user_input, collection):
    """
    Orchestrates the coach's response with full context awareness.
    """
    # 1. Get comprehensive user context from SQLite
    user_context = get_user_context()

    # 2. Get current time context
    now = datetime.datetime.now()
    time_context = f"Current time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"

    # 3. Find relevant knowledge from ChromaDB
    knowledge_context = query_knowledge_base(collection, user_input)
    knowledge_prompt_section = "\n\n--- Relevant Knowledge ---\n" + \
        "\n".join(knowledge_context) if knowledge_context else ""

    # 4. Build a comprehensive prompt for Gemini
    prompt = f"""
    You are a world-class personal learning coach based on the 'iCanStudy' methodology.
    Your tone is encouraging, precise, and proactive. You provide clear, actionable guidance and ask insightful questions.

    IMPORTANT INSTRUCTIONS:
    - Keep responses concise (2-3 sentences max)
    - Always end with a question to keep the conversation going
    - Use the user's name when appropriate
    - Reference their current progress and context
    - If they ask about time, date, or personal info, use the context provided
    - Focus on their current main focus area but integrate other phases when helpful

    {time_context}

    USER CONTEXT:
    {user_context}

    {knowledge_prompt_section}

    User's Input: "{user_input}"

    Your Response:
    """

    # 5. Get and return the response
    coach_response = get_gemini_response(prompt)

    # 6. Log this interaction (basic logging)
    log_interaction(user_input, coach_response)

    return coach_response


def log_interaction(user_input, coach_response):
    """Logs the interaction for future reference."""
    # This could be expanded to store conversations in the database
    # For now, we'll keep it simple
    pass


def add_or_update_skill(skill_name, phase, level):
    """Adds or updates a skill in the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_time = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT OR REPLACE INTO Skills (skill_name, phase, level, last_updated)
        VALUES (?, ?, ?, ?)
    """, (skill_name, phase, level, current_time))

    conn.commit()
    conn.close()
    print(f"Updated skill: {skill_name} ({phase}) -> {level}")


def log_practice_block(skill_name, duration_minutes, difficulty_notes=""):
    """Logs a practice session."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get skill ID
    cursor.execute("SELECT id FROM Skills WHERE skill_name = ?", (skill_name,))
    skill_result = cursor.fetchone()

    if not skill_result:
        print(f"Skill '{skill_name}' not found. Adding it first...")
        add_or_update_skill(skill_name, "Enablers", "developing")
        cursor.execute(
            "SELECT id FROM Skills WHERE skill_name = ?", (skill_name,))
        skill_result = cursor.fetchone()

    skill_id = skill_result[0]
    current_date = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT INTO Practice_Blocks (date, skill_id, duration_minutes, difficulty_notes)
        VALUES (?, ?, ?, ?)
    """, (current_date, skill_id, duration_minutes, difficulty_notes))

    conn.commit()
    conn.close()
    print(
        f"Logged practice session: {skill_name} for {duration_minutes} minutes")


def add_distraction_habit(habit_type, description):
    """Adds a new distraction or habit to track."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        INSERT INTO Distraction_Habits (type, description, status)
        VALUES (?, ?, 'active')
    """, (habit_type, description))

    conn.commit()
    conn.close()
    print(f"Added {habit_type}: {description}")


def update_main_focus(new_focus):
    """Updates the user's main focus area."""
    if new_focus not in ['Enablers', 'Retrieval', 'Encoding']:
        print("Invalid focus area. Must be: Enablers, Retrieval, or Encoding")
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        UPDATE User_Profile SET current_main_focus = ? WHERE id = 1
    """, (new_focus,))

    conn.commit()
    conn.close()
    print(f"Updated main focus to: {new_focus}")


def handle_special_commands(user_input, collection):
    """Handles special commands for data management."""
    user_input_lower = user_input.lower().strip()

    # Practice block logging
    if user_input_lower.startswith("log practice"):
        try:
            # Expected format: "log practice [skill] [duration] [notes]"
            parts = user_input.split(" ", 3)
            if len(parts) >= 3:
                skill = parts[2]
                duration = int(parts[3]) if len(
                    parts) > 3 and parts[3].isdigit() else 30
                notes = parts[4] if len(parts) > 4 else ""
                log_practice_block(skill, duration, notes)
                return f"Practice session logged! How did that feel?"
        except (ValueError, IndexError):
            return "Format: 'log practice [skill_name] [duration_minutes] [optional_notes]'"

    # Skill level updates
    elif user_input_lower.startswith("update skill"):
        try:
            # Expected format: "update skill [skill] [phase] [level]"
            parts = user_input.split(" ", 4)
            if len(parts) >= 5:
                skill = parts[2]
                phase = parts[3]
                level = parts[4]
                add_or_update_skill(skill, phase, level)
                return f"Skill updated! What would you like to work on next?"
        except IndexError:
            return "Format: 'update skill [skill_name] [phase] [level]'"

    # Focus area changes
    elif user_input_lower.startswith("change focus"):
        try:
            parts = user_input.split(" ", 2)
            if len(parts) >= 3:
                new_focus = parts[2].title()
                update_main_focus(new_focus)
                return f"Focus changed to {new_focus}! Ready to dive deeper into this area?"
        except IndexError:
            return "Format: 'change focus [Enablers/Retrieval/Encoding]'"

    # Add distractions
    elif user_input_lower.startswith("add distraction"):
        try:
            description = user_input[15:].strip()  # Remove "add distraction "
            add_distraction_habit("distraction", description)
            return f"Distraction noted. What strategies have worked for you before with similar challenges?"
        except:
            return "Format: 'add distraction [description]'"

    return None  # Not a special command


def check_proactive_triggers():
    """
    Checks for conditions to start a conversation proactively.
    """
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user name for personalization
    cursor.execute("SELECT name FROM User_Profile WHERE id = 1")
    name_result = cursor.fetchone()
    name = name_result[0] if name_result else "there"

    now = datetime.datetime.now()
    current_hour = now.hour
    current_weekday = now.weekday()  # 0 = Monday, 6 = Sunday

    # Morning greeting (6 AM - 12 PM)
    if 6 <= current_hour < 12:
        return f"Good morning, {name}! Ready to tackle your learning goals today? What's your main focus?"

    # Afternoon check-in (12 PM - 6 PM)
    elif 12 <= current_hour < 18:
        return f"Good afternoon, {name}! How's your learning progress going today?"

    # Evening reflection (6 PM - 10 PM)
    elif 18 <= current_hour < 22:
        return f"Good evening, {name}! Time to reflect on today's learning. What went well?"

    # Check for practice reminders
    cursor.execute("""
        SELECT MAX(date) FROM Practice_Blocks
    """)
    last_practice = cursor.fetchone()[0]

    if last_practice:
        last_practice_date = datetime.datetime.fromisoformat(last_practice)
        days_since_practice = (now - last_practice_date).days

        if days_since_practice >= 2:
            conn.close()
            return f"Hi {name}! I noticed it's been {days_since_practice} days since your last practice session. Ready to get back into it?"

    # Weekly reflection trigger (Friday or Saturday)
    if current_weekday in [4, 5]:  # Friday or Saturday
        conn.close()
        return f"Hey {name}! It's the weekend - perfect time for a weekly reflection. What were your biggest learning wins this week?"

    conn.close()
    return None


def main_loop(collection):
    """The main interactive loop for the coach."""
    print("Welcome to your Personalized AI Coach!")
    print("Commands: 'quit' to exit, 'help' for commands")
    print(
        "Special commands: 'log practice [skill] [minutes]', 'update skill [name] [phase] [level]', 'change focus [area]'")

    # Simple proactive trigger check at the start of the session
    initial_prompt = check_proactive_triggers()
    if initial_prompt:
        print(f"\nCoach: {initial_prompt}")

    while True:
        try:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                print("Coach: Great session. Keep up the momentum!")
                break

            if user_input.lower() == 'help':
                print("\nAvailable commands:")
                print(
                    "- 'log practice [skill_name] [duration_minutes] [optional_notes]'")
                print("- 'update skill [skill_name] [phase] [level]'")
                print("- 'change focus [Enablers/Retrieval/Encoding]'")
                print("- 'add distraction [description]'")
                print("- Ask me anything about your learning journey!")
                continue

            if user_input:
                # Check for special commands first
                special_response = handle_special_commands(
                    user_input, collection)
                if special_response:
                    print(f"\nCoach: {special_response}")
                else:
                    # Regular conversation
                    coach_response = manage_coach_response(
                        user_input, collection)
                    print(f"\nCoach: {coach_response}")

        except (KeyboardInterrupt, EOFError):
            print("\nCoach: Session ended. See you next time!")
            break


# --- SCRIPT EXECUTION ---

if __name__ == "__main__":
    knowledge_collection = initialize_system()
    main_loop(knowledge_collection)
