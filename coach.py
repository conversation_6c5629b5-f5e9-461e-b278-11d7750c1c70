import os
import sqlite3
import json
import datetime
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
KNOWLEDGE_BASE_DIR = "knowledge_data"
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"


# --- SYSTEM INITIALIZATION ---

def setup_directories():
    """Create necessary directories if they don't exist."""
    os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs(CHROMA_DB_PATH, exist_ok=True)


def create_db_tables(conn):
    """Create the SQLite database tables based on our English design."""
    cursor = conn.cursor()

    # User_Profile: Stores the user's general state.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Profile (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            current_main_focus TEXT DEFAULT 'Enablers'
        )
    ''')

    # Skills: Tracks the user's skill level in different techniques.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Skills (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            skill_name TEXT UNIQUE NOT NULL,
            phase TEXT NOT NULL,
            level TEXT DEFAULT 'unknown',
            last_updated TEXT NOT NULL
        )
    ''')

    # Practice_Blocks: Logs practice sessions.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Practice_Blocks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT NOT NULL,
            skill_id INTEGER,
            duration_minutes INTEGER,
            difficulty_notes TEXT,
            FOREIGN KEY (skill_id) REFERENCES Skills (id)
        )
    ''')

    # Distraction_Habits: Keeps an inventory of personal obstacles.
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Distraction_Habits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type TEXT NOT NULL,
            description TEXT NOT NULL,
            status TEXT DEFAULT 'active'
        )
    ''')

    conn.commit()
    print("Database tables checked/created successfully.")


def setup_database():
    """Initializes the SQLite database and its tables."""
    conn = sqlite3.connect(DB_PATH)
    create_db_tables(conn)

    # Check if user profile exists, if not, create one.
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM User_Profile WHERE id = 1")
    if cursor.fetchone() is None:
        print("No user profile found. Let's create one.")
        name = input("What is your name? ")
        cursor.execute(
            "INSERT INTO User_Profile (id, name) VALUES (?, ?)", (1, name))
        conn.commit()
        print(f"Welcome, {name}! Your profile has been created.")

    conn.close()


def load_knowledge_base_if_empty(collection):
    """Loads knowledge from JSON files into ChromaDB if the collection is empty."""
    if collection.count() > 0:
        print("Knowledge base is already loaded.")
        return

    print("Knowledge base is empty. Loading from JSON files...")
    documents = []
    metadatas = []
    ids = []

    for filename in os.listdir(KNOWLEDGE_BASE_DIR):
        if filename.endswith('.json'):
            filepath = os.path.join(KNOWLEDGE_BASE_DIR, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                concepts = json.load(f)
                for concept in concepts:
                    # The document for embedding is a rich combination of its parts
                    doc_content = (
                        f"Concept: {concept['concept_name']}. "
                        f"Summary: {concept['summary']}. "
                        f"Instructions: {' '.join(concept['instructions'])}. "
                        f"Context: {concept['additional_context']}"
                    )
                    documents.append(doc_content)
                    metadatas.append({
                        "concept_id": concept['concept_id'],
                        "concept_name": concept['concept_name'],
                        "ics_phase": concept['ics_phase']
                    })
                    ids.append(concept['concept_id'])

    if ids:
        collection.add(documents=documents, metadatas=metadatas, ids=ids)
        print(
            f"Successfully loaded {len(ids)} concepts into the knowledge base.")


def configure_gemini():
    """Configures the Gemini API."""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError(
            "GOOGLE_API_KEY not found in .env file. Please set it.")
    genai.configure(api_key=api_key)


def initialize_system():
    """Runs all setup functions to prepare the application."""
    print("Initializing The Coach...")
    setup_directories()
    setup_database()

    # Setup ChromaDB
    chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
    collection = chroma_client.get_or_create_collection(
        name=KNOWLEDGE_BASE_COLLECTION_NAME)
    load_knowledge_base_if_empty(collection)

    # Configure Gemini
    configure_gemini()

    print("Initialization complete. The Coach is ready.\n")
    return collection


# --- CORE COACH LOGIC ---

def query_knowledge_base(collection, query_text, n_results=3):
    """Queries the ChromaDB knowledge base for relevant concepts."""
    results = collection.query(query_texts=[query_text], n_results=n_results)
    return results['documents'][0] if results else []


def get_gemini_response(prompt):
    """Gets a response from the Gemini model."""
    model = genai.GenerativeModel('gemini-2.0-flash')
    response = model.generate_content(prompt)
    return response.text


def manage_coach_response(user_input, collection):
    """
    Orchestrates the coach's response.
    1. Gets user context from SQLite (TODO).
    2. Finds relevant knowledge from ChromaDB.
    3. Builds a prompt for Gemini.
    4. Gets and returns the response.
    5. Updates user memory in SQLite (TODO).
    """
    # 1. Get user context from SQLite (Future Implementation)
    # TODO: Fetch user's current focus, recent activities, etc.
    user_context = "User's current main focus is on 'Enablers'."

    # 2. Find relevant knowledge from ChromaDB
    knowledge_context = query_knowledge_base(collection, user_input)
    knowledge_prompt_section = "\n\n--- Relevant Knowledge ---\n" + \
        "\n".join(knowledge_context)

    # 3. Build a prompt for Gemini
    prompt = f"""
    You are a world-class personal learning coach based on the 'iCanStudy' methodology.
    Your tone is encouraging, precise, and proactive. You don't give long sermons; you ask insightful questions and provide clear, actionable guidance.

    User's Current Context: {user_context}

    Based on the following relevant knowledge and the user's input, generate a helpful response.
    If the user asks a question, use the knowledge to answer it.
    If the user describes a problem (e.g., "I feel stuck"), use the knowledge to suggest a specific technique.
    Always be concise and end with a question to keep the conversation going.

    {knowledge_prompt_section}

    User's Input: "{user_input}"

    Your Response:
    """

    # 4. Get and return the response
    coach_response = get_gemini_response(prompt)

    # 5. Update user memory (Future Implementation)
    # TODO: Add logic to parse the conversation and update SQLite DB
    # (e.g., log a practice block, update skill level)

    return coach_response


def check_proactive_triggers():
    """
    Checks for conditions to start a conversation proactively.
    This is a placeholder for a more advanced system. In a terminal app,
    we can run this once at the start of the main loop.
    """
    # TODO: Implement proactive triggers (e.g., morning greeting, practice reminder).
    # For now, this can be a simple greeting.
    current_hour = datetime.datetime.now().hour
    if 6 <= current_hour < 12:
        return "Good morning! What's our main focus for today?"
    # Add more triggers for practice reminders, weekly reflections etc.
    return None


def main_loop(collection):
    """The main interactive loop for the coach."""
    print("Welcome to your Personalized AI Coach. Type 'quit' to exit.")

    # Simple proactive trigger check at the start of the session
    initial_prompt = check_proactive_triggers()
    if initial_prompt:
        print(f"\nCoach: {initial_prompt}")

    while True:
        try:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                print("Coach: Great session. Keep up the momentum!")
                break

            if user_input:
                coach_response = manage_coach_response(user_input, collection)
                print(f"\nCoach: {coach_response}")

        except (KeyboardInterrupt, EOFError):
            print("\nCoach: Session ended. See you next time!")
            break


# --- SCRIPT EXECUTION ---

if __name__ == "__main__":
    knowledge_collection = initialize_system()
    main_loop(knowledge_collection)
