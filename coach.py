import os
import sqlite3
import json
import datetime
import chromadb
import google.generativeai as genai
from dotenv import load_dotenv

# --- CONFIGURATION AND CONSTANTS ---
load_dotenv()

DB_PATH = "db/user_memory.db"
KNOWLEDGE_BASE_DIR = "knowledge_data"
CHROMA_DB_PATH = "db/knowledge_vectordb"
KNOWLEDGE_BASE_COLLECTION_NAME = "ics_knowledge_base"


# --- SYSTEM INITIALIZATION ---

def setup_directories():
    """Create necessary directories if they don't exist."""
    os.makedirs(KNOWLEDGE_BASE_DIR, exist_ok=True)
    os.makedirs("db", exist_ok=True)
    os.makedirs(CHROMA_DB_PATH, exist_ok=True)


def create_db_tables(conn):
    """Create the comprehensive SQLite database tables for advanced coaching."""
    cursor = conn.cursor()

    # User_Profile: Comprehensive user information and coaching state
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Profile (
            user_id INTEGER PRIMARY KEY,
            name TEXT NOT NULL,
            start_date DATE DEFAULT CURRENT_DATE,
            archetype TEXT DEFAULT 'Unchained',
            current_main_focus TEXT DEFAULT 'Enablers',
            timezone TEXT DEFAULT 'UTC',
            preferred_session_length INTEGER DEFAULT 25,
            daily_study_goal_minutes INTEGER DEFAULT 120,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_active DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Goals: User's learning objectives and targets
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Goals (
            goal_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            is_long_term BOOLEAN DEFAULT 0,
            target_date DATE,
            status TEXT DEFAULT 'Active',
            priority_level INTEGER DEFAULT 3,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Skills: Detailed competence tracking linked to knowledge base
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Skills (
            skill_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            concept_id TEXT NOT NULL,
            competence_level TEXT DEFAULT 'Unknown',
            last_practiced_date DATE,
            practice_count INTEGER DEFAULT 0,
            is_priority BOOLEAN DEFAULT 0,
            confidence_score REAL DEFAULT 0.0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Practice_Log: Detailed session tracking with user feedback
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Practice_Log (
            log_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            skill_id INTEGER,
            log_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            duration_minutes INTEGER NOT NULL,
            user_notes_difficulty TEXT,
            confidence_score INTEGER CHECK (confidence_score >= 1 AND confidence_score <= 5),
            coach_intervention_id TEXT,
            session_type TEXT DEFAULT 'practice',
            effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
            FOREIGN KEY (skill_id) REFERENCES Skills (skill_id)
        )
    ''')

    # Distraction_Habits: Comprehensive habit and obstacle tracking
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Distraction_Habits (
            habit_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            description TEXT NOT NULL,
            trigger_description TEXT,
            mitigation_strategy_id TEXT,
            status TEXT DEFAULT 'Identified',
            frequency TEXT DEFAULT 'Unknown',
            impact_level INTEGER DEFAULT 3 CHECK (impact_level >= 1 AND impact_level <= 5),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_occurred DATETIME,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Scheduled_Events: Comprehensive calendar and planning system
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Scheduled_Events (
            event_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            event_description TEXT NOT NULL,
            event_type TEXT NOT NULL,
            scheduled_start_time DATETIME NOT NULL,
            scheduled_end_time DATETIME NOT NULL,
            is_completed BOOLEAN DEFAULT 0,
            is_recurring BOOLEAN DEFAULT 0,
            recurrence_pattern TEXT,
            related_skill_id INTEGER,
            related_goal_id INTEGER,
            priority_level INTEGER DEFAULT 3,
            location TEXT,
            preparation_time_minutes INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id),
            FOREIGN KEY (related_skill_id) REFERENCES Skills (skill_id),
            FOREIGN KEY (related_goal_id) REFERENCES Goals (goal_id)
        )
    ''')

    # Coach_Interactions: Track coaching conversations and interventions
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Coach_Interactions (
            interaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            interaction_type TEXT NOT NULL,
            trigger_reason TEXT,
            coach_message TEXT NOT NULL,
            user_response TEXT,
            concepts_referenced TEXT,
            effectiveness_rating INTEGER CHECK (effectiveness_rating >= 1 AND effectiveness_rating <= 5),
            follow_up_needed BOOLEAN DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # User_Assessment: Periodic assessments and roadmap updates
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS User_Assessment (
            assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            assessment_type TEXT NOT NULL,
            overall_progress_score REAL,
            main_strengths TEXT,
            main_weaknesses TEXT,
            recommended_focus_areas TEXT,
            roadmap_adjustments TEXT,
            next_assessment_date DATE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Session_Logs: Track daily interaction patterns
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Session_Logs (
            session_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            session_date DATE NOT NULL,
            session_start_time DATETIME,
            session_end_time DATETIME,
            total_interactions INTEGER DEFAULT 0,
            main_topics_discussed TEXT,
            problems_identified TEXT,
            solutions_suggested TEXT,
            user_mood_indicators TEXT,
            effectiveness_score REAL,
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    # Pattern_Analysis: Store identified behavioral patterns
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Pattern_Analysis (
            pattern_id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            pattern_type TEXT NOT NULL,
            pattern_description TEXT NOT NULL,
            frequency TEXT NOT NULL,
            trigger_conditions TEXT,
            impact_assessment TEXT,
            suggested_interventions TEXT,
            pattern_strength REAL,
            first_detected DATE,
            last_observed DATE,
            status TEXT DEFAULT 'active',
            FOREIGN KEY (user_id) REFERENCES User_Profile (user_id)
        )
    ''')

    conn.commit()
    print("Advanced database schema with pattern recognition created successfully.")


def setup_database():
    """Initializes the SQLite database and its tables."""
    conn = sqlite3.connect(DB_PATH)
    create_db_tables(conn)

    # Check if user profile exists, if not, create one.
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM User_Profile WHERE user_id = 1")
    if cursor.fetchone() is None:
        print("Welcome to your AI Learning Coach!")
        print("Let's set up your profile for personalized coaching.")

        name = input("What is your name? ")

        print("\nWhat's your main learning goal right now?")
        goal_description = input("Goal: ")

        print("\nHow many minutes per day do you want to dedicate to focused learning?")
        daily_goal = input("Daily goal (default 120 minutes): ") or "120"

        # Create user profile
        cursor.execute("""
            INSERT INTO User_Profile (user_id, name, daily_study_goal_minutes)
            VALUES (?, ?, ?)
        """, (1, name, int(daily_goal)))

        # Create initial goal
        cursor.execute("""
            INSERT INTO Goals (user_id, description, is_long_term)
            VALUES (?, ?, ?)
        """, (1, goal_description, True))

        conn.commit()
        print(f"\nWelcome, {name}! Your coaching profile has been created.")
        print("I'll help you achieve your learning goals through personalized guidance.")

    conn.close()


def load_knowledge_base_if_empty(collection):
    """Loads knowledge from JSON files into ChromaDB if the collection is empty."""
    if collection.count() > 0:
        print("Knowledge base is already loaded.")
        return

    print("Knowledge base is empty. Loading from JSON files...")
    documents = []
    metadatas = []
    ids = []

    for filename in os.listdir(KNOWLEDGE_BASE_DIR):
        if filename.endswith('.json'):
            filepath = os.path.join(KNOWLEDGE_BASE_DIR, filename)
            with open(filepath, 'r', encoding='utf-8') as f:
                concepts = json.load(f)
                for concept in concepts:
                    # The document for embedding is a rich combination of its parts
                    doc_content = (
                        f"Concept: {concept['concept_name']}. "
                        f"Summary: {concept['summary']}. "
                        f"Instructions: {' '.join(concept['instructions'])}. "
                        f"Context: {concept['additional_context']}"
                    )
                    documents.append(doc_content)
                    metadatas.append({
                        "concept_id": concept['concept_id'],
                        "concept_name": concept['concept_name'],
                        "ics_phase": concept['ics_phase']
                    })
                    ids.append(concept['concept_id'])

    if ids:
        collection.add(documents=documents, metadatas=metadatas, ids=ids)
        print(
            f"Successfully loaded {len(ids)} concepts into the knowledge base.")


def configure_gemini():
    """Configures the Gemini API."""
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError(
            "GOOGLE_API_KEY not found in .env file. Please set it.")
    genai.configure(api_key=api_key)


def initialize_system():
    """Runs all setup functions to prepare the application."""
    print("Initializing The Coach...")
    setup_directories()
    setup_database()

    # Setup ChromaDB
    chroma_client = chromadb.PersistentClient(path=CHROMA_DB_PATH)
    collection = chroma_client.get_or_create_collection(
        name=KNOWLEDGE_BASE_COLLECTION_NAME)
    load_knowledge_base_if_empty(collection)

    # Configure Gemini
    configure_gemini()

    print("Initialization complete. The Coach is ready.\n")
    return collection


# --- CORE COACH LOGIC ---

def query_knowledge_base(collection, query_text, n_results=3):
    """Queries the ChromaDB knowledge base for relevant concepts."""
    results = collection.query(query_texts=[query_text], n_results=n_results)
    return results['documents'][0] if results else []


def get_gemini_response(prompt):
    """Gets a response from the Gemini model."""
    model = genai.GenerativeModel('gemini-2.0-flash')
    response = model.generate_content(prompt)
    return response.text


def get_user_context():
    """Gets comprehensive user context from the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user profile
    cursor.execute("""
        SELECT name, current_main_focus, daily_study_goal_minutes, archetype
        FROM User_Profile WHERE user_id = 1
    """)
    profile = cursor.fetchone()

    if not profile:
        conn.close()
        return "No user profile found."

    name, main_focus, daily_goal, archetype = profile

    # Get current goals
    cursor.execute("""
        SELECT description, target_date, status
        FROM Goals
        WHERE user_id = 1 AND status = 'Active'
        ORDER BY priority_level DESC
        LIMIT 3
    """)
    goals = cursor.fetchall()

    # Get recent practice sessions
    cursor.execute("""
        SELECT pl.log_date, s.concept_id, pl.duration_minutes,
               pl.user_notes_difficulty, pl.confidence_score
        FROM Practice_Log pl
        JOIN Skills s ON pl.skill_id = s.skill_id
        WHERE pl.user_id = 1
        ORDER BY pl.log_date DESC
        LIMIT 3
    """)
    recent_practice = cursor.fetchall()

    # Get current skill levels
    cursor.execute("""
        SELECT concept_id, competence_level, confidence_score, practice_count
        FROM Skills
        WHERE user_id = 1 AND competence_level != 'Unknown'
        ORDER BY last_updated DESC
        LIMIT 5
    """)
    skills = cursor.fetchall()

    # Get active distractions
    cursor.execute("""
        SELECT description, trigger_description, impact_level
        FROM Distraction_Habits
        WHERE user_id = 1 AND status = 'Identified'
        ORDER BY impact_level DESC
        LIMIT 3
    """)
    distractions = cursor.fetchall()

    # Get upcoming scheduled events
    cursor.execute("""
        SELECT event_description, scheduled_start_time, event_type
        FROM Scheduled_Events
        WHERE user_id = 1 AND scheduled_start_time > datetime('now')
        ORDER BY scheduled_start_time
        LIMIT 3
    """)
    upcoming_events = cursor.fetchall()

    conn.close()

    # Build comprehensive context string
    context = f"User: {name} (Archetype: {archetype})\n"
    context += f"Current Main Focus: {main_focus}\n"
    context += f"Daily Study Goal: {daily_goal} minutes\n"

    if goals:
        context += "\nActive Goals:\n"
        for desc, target, status in goals:
            context += f"- {desc} (Target: {target or 'No deadline'}, Status: {status})\n"

    if skills:
        context += "\nCurrent Skills:\n"
        for concept_id, level, confidence, practice_count in skills:
            context += f"- {concept_id}: {level} (Confidence: {confidence:.1f}, Practiced: {practice_count}x)\n"

    if recent_practice:
        context += "\nRecent Practice Sessions:\n"
        for date, concept_id, duration, notes, confidence in recent_practice:
            context += f"- {date}: {concept_id} for {duration} minutes"
            if confidence:
                context += f" (Confidence: {confidence}/5)"
            if notes:
                context += f" (Notes: {notes})"
            context += "\n"

    if distractions:
        context += "\nActive Challenges:\n"
        for desc, trigger, impact in distractions:
            context += f"- {desc} (Trigger: {trigger or 'Unknown'}, Impact: {impact}/5)\n"

    if upcoming_events:
        context += "\nUpcoming Schedule:\n"
        for desc, start_time, event_type in upcoming_events:
            context += f"- {start_time}: {desc} ({event_type})\n"

    return context


def manage_coach_response(user_input, collection, session_id=None):
    """
    Orchestrates the coach's response with intelligent context awareness.
    """
    # 1. Get intelligent context (includes patterns and history)
    user_context = get_intelligent_context()

    # 2. Get current time context
    now = datetime.datetime.now()
    time_context = f"Current time: {now.strftime('%A, %B %d, %Y at %I:%M %p')}"

    # 3. Find relevant knowledge from ChromaDB
    knowledge_context = query_knowledge_base(collection, user_input)
    knowledge_prompt_section = "\n\n--- Relevant Knowledge ---\n" + \
        "\n".join(knowledge_context) if knowledge_context else ""

    # 4. Check for pattern-based interventions
    pattern_intervention = get_pattern_intervention(user_input, collection)
    if pattern_intervention:
        knowledge_prompt_section += f"\n\n--- Pattern-Based Guidance ---\n{pattern_intervention}"

    # 5. Build a comprehensive prompt for Gemini
    prompt = f"""
    You are a world-class personal learning coach based on the 'iCanStudy' methodology.
    Your tone is encouraging, precise, and proactive. You provide clear, actionable guidance and ask insightful questions.

    IMPORTANT INSTRUCTIONS:
    - Keep responses concise (2-3 sentences max)
    - Always end with a question to keep the conversation going
    - Use the user's name when appropriate
    - Reference their current progress and context
    - If they ask about time, date, or personal info, use the context provided
    - Focus on their current main focus area but integrate other phases when helpful
    - If patterns are identified, address them with specific knowledge-based solutions
    - Be proactive in guiding the user toward better learning habits

    {time_context}

    USER CONTEXT:
    {user_context}

    {knowledge_prompt_section}

    User's Input: "{user_input}"

    Your Response:
    """

    # 6. Get and return the response
    coach_response = get_gemini_response(prompt)

    # 7. Log this interaction with pattern analysis
    log_interaction(user_input, coach_response, session_id)

    return coach_response


def get_pattern_intervention(user_input, collection):
    """Gets specific interventions based on detected patterns."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Check for active patterns that might be relevant
    cursor.execute("""
        SELECT pattern_description, suggested_interventions
        FROM Pattern_Analysis
        WHERE user_id = 1 AND status = 'active' AND pattern_strength > 0.6
    """)
    patterns = cursor.fetchall()

    conn.close()

    user_input_lower = user_input.lower()
    interventions = []

    for description, suggested_interventions in patterns:
        # Check if user input relates to this pattern
        pattern_keywords = description.lower().split()
        if any(keyword in user_input_lower for keyword in pattern_keywords):
            if suggested_interventions:
                # Query knowledge base for specific interventions
                intervention_concepts = query_knowledge_base(
                    collection, description, n_results=2)
                if intervention_concepts:
                    interventions.extend(intervention_concepts)

    return "\n".join(interventions) if interventions else None


def start_session():
    """Starts a new coaching session and returns session_id."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_date = datetime.datetime.now().date()
    current_time = datetime.datetime.now()

    # Check if there's already a session today
    cursor.execute("""
        SELECT session_id FROM Session_Logs
        WHERE user_id = 1 AND session_date = ?
    """, (current_date,))

    existing_session = cursor.fetchone()

    if existing_session:
        session_id = existing_session[0]
        # Update session start time if resuming
        cursor.execute("""
            UPDATE Session_Logs
            SET session_start_time = ?
            WHERE session_id = ?
        """, (current_time, session_id))
    else:
        # Create new session
        cursor.execute("""
            INSERT INTO Session_Logs (user_id, session_date, session_start_time)
            VALUES (?, ?, ?)
        """, (1, current_date, current_time))
        session_id = cursor.lastrowid

    conn.commit()
    conn.close()
    return session_id


def end_session(session_id):
    """Ends the current session and analyzes patterns."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_time = datetime.datetime.now()

    # Update session end time
    cursor.execute("""
        UPDATE Session_Logs
        SET session_end_time = ?
        WHERE session_id = ?
    """, (current_time, session_id))

    # Analyze patterns from today's session
    analyze_session_patterns(session_id)

    conn.commit()
    conn.close()


def log_interaction(user_input, coach_response, session_id=None):
    """Logs the interaction with pattern analysis."""
    if not session_id:
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    try:
        # Log to Coach_Interactions
        cursor.execute("""
            INSERT INTO Coach_Interactions
            (user_id, interaction_type, coach_message, user_response)
            VALUES (?, ?, ?, ?)
        """, (1, 'reactive', coach_response, user_input))

        # Update session interaction count
        cursor.execute("""
            UPDATE Session_Logs
            SET total_interactions = total_interactions + 1
            WHERE session_id = ?
        """, (session_id,))

        # Check for distraction patterns in the same connection
        check_distraction_patterns_with_conn(user_input, conn)

        conn.commit()
    finally:
        conn.close()


def check_distraction_patterns_with_conn(user_input, conn):
    """Analyzes user input for distraction patterns using existing connection."""
    cursor = conn.cursor()

    # Common distraction keywords
    distraction_keywords = ['chess', 'social media', 'phone', 'youtube', 'netflix',
                            'instagram', 'twitter', 'facebook', 'games', 'tv']

    user_input_lower = user_input.lower()

    for keyword in distraction_keywords:
        if keyword in user_input_lower:
            # Check if this pattern already exists
            cursor.execute("""
                SELECT pattern_id, pattern_strength FROM Pattern_Analysis
                WHERE user_id = 1 AND pattern_type = 'distraction'
                AND pattern_description LIKE ?
            """, (f'%{keyword}%',))

            existing_pattern = cursor.fetchone()

            if existing_pattern:
                # Update existing pattern
                pattern_id, current_strength = existing_pattern
                new_strength = min(1.0, current_strength + 0.1)
                cursor.execute("""
                    UPDATE Pattern_Analysis
                    SET pattern_strength = ?, last_observed = ?
                    WHERE pattern_id = ?
                """, (new_strength, datetime.datetime.now().date(), pattern_id))
            else:
                # Create new pattern
                cursor.execute("""
                    INSERT INTO Pattern_Analysis
                    (user_id, pattern_type, pattern_description, frequency,
                     pattern_strength, first_detected, last_observed)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (1, 'distraction', f'{keyword.title()} distraction pattern',
                      'situational', 0.3, datetime.datetime.now().date(),
                      datetime.datetime.now().date()))


def check_distraction_patterns(user_input):
    """Analyzes user input for distraction patterns."""
    conn = sqlite3.connect(DB_PATH)
    try:
        check_distraction_patterns_with_conn(user_input, conn)
        conn.commit()
    finally:
        conn.close()


def analyze_session_patterns(session_id):
    """Analyzes patterns from the current session."""
    # This would contain more sophisticated pattern analysis
    # For now, we'll keep it simple
    pass


def get_intelligent_context():
    """Gets smart context based on patterns and recent history."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get basic user context
    basic_context = get_user_context()

    # Get recent patterns (last 30 days)
    cursor.execute("""
        SELECT pattern_type, pattern_description, pattern_strength,
               suggested_interventions, status
        FROM Pattern_Analysis
        WHERE user_id = 1 AND last_observed >= date('now', '-30 days')
        AND pattern_strength > 0.5
        ORDER BY pattern_strength DESC
        LIMIT 3
    """)
    patterns = cursor.fetchall()

    # Get yesterday's session summary
    cursor.execute("""
        SELECT main_topics_discussed, problems_identified, effectiveness_score
        FROM Session_Logs
        WHERE user_id = 1 AND session_date = date('now', '-1 day')
    """)
    yesterday = cursor.fetchone()

    conn.close()

    # Build intelligent context
    context = basic_context

    if patterns:
        context += "\n\nIdentified Patterns:\n"
        for pattern_type, description, strength, interventions, status in patterns:
            context += f"- {description} (Strength: {strength:.1f}, Status: {status})\n"

    if yesterday:
        topics, problems, effectiveness = yesterday
        if topics or problems:
            context += f"\nYesterday's Session:\n"
            if topics:
                context += f"- Topics: {topics}\n"
            if problems:
                context += f"- Challenges: {problems}\n"
            if effectiveness:
                context += f"- Effectiveness: {effectiveness:.1f}/5\n"

    return context


def add_or_update_skill(skill_name, phase, level):
    """Adds or updates a skill in the database."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    current_time = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT OR REPLACE INTO Skills (user_id, concept_id, competence_level, last_updated)
        VALUES (?, ?, ?, ?)
    """, (1, skill_name, level, current_time))

    conn.commit()
    conn.close()
    print(f"Updated skill: {skill_name} ({phase}) -> {level}")


def log_practice_block(skill_name, duration_minutes, difficulty_notes=""):
    """Logs a practice session."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get skill ID using new schema
    cursor.execute(
        "SELECT skill_id FROM Skills WHERE concept_id = ?", (skill_name,))
    skill_result = cursor.fetchone()

    if not skill_result:
        print(f"Skill '{skill_name}' not found. Adding it first...")
        add_or_update_skill(skill_name, "Enablers", "developing")
        cursor.execute(
            "SELECT skill_id FROM Skills WHERE concept_id = ?", (skill_name,))
        skill_result = cursor.fetchone()

    skill_id = skill_result[0]
    current_date = datetime.datetime.now().isoformat()

    cursor.execute("""
        INSERT INTO Practice_Log (user_id, skill_id, duration_minutes, user_notes_difficulty)
        VALUES (?, ?, ?, ?)
    """, (1, skill_id, duration_minutes, difficulty_notes))

    conn.commit()
    conn.close()
    print(
        f"Logged practice session: {skill_name} for {duration_minutes} minutes")


def add_distraction_habit(habit_type, description):
    """Adds a new distraction or habit to track."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        INSERT INTO Distraction_Habits (user_id, description, status)
        VALUES (?, ?, 'Identified')
    """, (1, description))

    conn.commit()
    conn.close()
    print(f"Added {habit_type}: {description}")


def update_main_focus(new_focus):
    """Updates the user's main focus area."""
    if new_focus not in ['Enablers', 'Retrieval', 'Encoding']:
        print("Invalid focus area. Must be: Enablers, Retrieval, or Encoding")
        return

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute("""
        UPDATE User_Profile SET current_main_focus = ? WHERE user_id = 1
    """, (new_focus,))

    conn.commit()
    conn.close()
    print(f"Updated main focus to: {new_focus}")


def handle_special_commands(user_input, collection):
    """Handles special commands for data management."""
    user_input_lower = user_input.lower().strip()

    # Practice block logging
    if user_input_lower.startswith("log practice"):
        try:
            # Expected format: "log practice [skill] [duration] [notes]"
            parts = user_input.split(" ", 3)
            if len(parts) >= 3:
                skill = parts[2]
                duration = int(parts[3]) if len(
                    parts) > 3 and parts[3].isdigit() else 30
                notes = parts[4] if len(parts) > 4 else ""
                log_practice_block(skill, duration, notes)
                return f"Practice session logged! How did that feel?"
        except (ValueError, IndexError):
            return "Format: 'log practice [skill_name] [duration_minutes] [optional_notes]'"

    # Skill level updates
    elif user_input_lower.startswith("update skill"):
        try:
            # Expected format: "update skill [skill] [phase] [level]"
            parts = user_input.split(" ", 4)
            if len(parts) >= 5:
                skill = parts[2]
                phase = parts[3]
                level = parts[4]
                add_or_update_skill(skill, phase, level)
                return f"Skill updated! What would you like to work on next?"
        except IndexError:
            return "Format: 'update skill [skill_name] [phase] [level]'"

    # Focus area changes
    elif user_input_lower.startswith("change focus"):
        try:
            parts = user_input.split(" ", 2)
            if len(parts) >= 3:
                new_focus = parts[2].title()
                update_main_focus(new_focus)
                return f"Focus changed to {new_focus}! Ready to dive deeper into this area?"
        except IndexError:
            return "Format: 'change focus [Enablers/Retrieval/Encoding]'"

    # Add distractions
    elif user_input_lower.startswith("add distraction"):
        try:
            description = user_input[15:].strip()  # Remove "add distraction "
            add_distraction_habit("distraction", description)
            return f"Distraction noted. What strategies have worked for you before with similar challenges?"
        except:
            return "Format: 'add distraction [description]'"

    return None  # Not a special command


def check_proactive_triggers():
    """
    Checks for conditions to start a conversation proactively.
    """
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user name for personalization
    cursor.execute("SELECT name FROM User_Profile WHERE user_id = 1")
    name_result = cursor.fetchone()
    name = name_result[0] if name_result else "there"

    now = datetime.datetime.now()
    current_hour = now.hour
    current_weekday = now.weekday()  # 0 = Monday, 6 = Sunday

    # Morning greeting (6 AM - 12 PM)
    if 6 <= current_hour < 12:
        return f"Good morning, {name}! Ready to tackle your learning goals today? What's your main focus?"

    # Afternoon check-in (12 PM - 6 PM)
    elif 12 <= current_hour < 18:
        return f"Good afternoon, {name}! How's your learning progress going today?"

    # Evening reflection (6 PM - 10 PM)
    elif 18 <= current_hour < 22:
        return f"Good evening, {name}! Time to reflect on today's learning. What went well?"

    # Check for practice reminders
    cursor.execute("""
        SELECT MAX(log_date) FROM Practice_Log WHERE user_id = 1
    """)
    last_practice = cursor.fetchone()[0]

    if last_practice:
        last_practice_date = datetime.datetime.fromisoformat(last_practice)
        days_since_practice = (now - last_practice_date).days

        if days_since_practice >= 2:
            conn.close()
            return f"Hi {name}! I noticed it's been {days_since_practice} days since your last practice session. Ready to get back into it?"

    # Weekly reflection trigger (Friday or Saturday)
    if current_weekday in [4, 5]:  # Friday or Saturday
        conn.close()
        return f"Hey {name}! It's the weekend - perfect time for a weekly reflection. What were your biggest learning wins this week?"

    conn.close()
    return None


def main_loop(collection):
    """The main interactive loop for the coach with session management."""
    # Start session tracking
    session_id = start_session()

    print("Welcome to your Personalized AI Coach!")
    print("Commands: 'quit' to exit, 'help' for commands")
    print(
        "Special commands: 'log practice [skill] [minutes]', 'update skill [name] [phase] [level]', 'change focus [area]'")

    # Proactive trigger check with pattern-aware context
    initial_prompt = check_proactive_triggers_with_patterns(collection)
    if initial_prompt:
        print(f"\nCoach: {initial_prompt}")

    while True:
        try:
            user_input = input("\nYou: ")
            if user_input.lower() == 'quit':
                print("Coach: Great session. Keep up the momentum!")
                end_session(session_id)
                break

            if user_input.lower() == 'help':
                print("\nAvailable commands:")
                print(
                    "- 'log practice [skill_name] [duration_minutes] [optional_notes]'")
                print("- 'update skill [skill_name] [phase] [level]'")
                print("- 'change focus [Enablers/Retrieval/Encoding]'")
                print("- 'add distraction [description]'")
                print("- Ask me anything about your learning journey!")
                continue

            if user_input:
                # Check for special commands first
                special_response = handle_special_commands(
                    user_input, collection)
                if special_response:
                    print(f"\nCoach: {special_response}")
                    # Log special command interactions too
                    log_interaction(user_input, special_response, session_id)
                else:
                    # Regular conversation with session tracking
                    coach_response = manage_coach_response(
                        user_input, collection, session_id)
                    print(f"\nCoach: {coach_response}")

        except (KeyboardInterrupt, EOFError):
            print("\nCoach: Session ended. See you next time!")
            end_session(session_id)
            break


def check_proactive_triggers_with_patterns(collection):
    """Enhanced proactive triggers that consider patterns and history."""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # Get user name for personalization
    cursor.execute("SELECT name FROM User_Profile WHERE user_id = 1")
    name_result = cursor.fetchone()
    name = name_result[0] if name_result else "there"

    now = datetime.datetime.now()
    current_hour = now.hour

    # Check for strong patterns that need immediate attention
    cursor.execute("""
        SELECT pattern_description, pattern_strength
        FROM Pattern_Analysis
        WHERE user_id = 1 AND status = 'active' AND pattern_strength > 0.8
        ORDER BY pattern_strength DESC
        LIMIT 1
    """)
    strong_pattern = cursor.fetchone()

    # Check yesterday's effectiveness
    cursor.execute("""
        SELECT effectiveness_score, problems_identified
        FROM Session_Logs
        WHERE user_id = 1 AND session_date = date('now', '-1 day')
    """)
    yesterday = cursor.fetchone()

    conn.close()

    # Priority 1: Address strong patterns
    if strong_pattern:
        description, strength = strong_pattern
        # Get intervention from knowledge base
        interventions = query_knowledge_base(
            collection, description, n_results=1)
        if interventions:
            return f"Hi {name}! I've noticed a strong pattern: {description}. Let's tackle this together. {interventions[0][:200]}... Ready to work on this?"

    # Priority 2: Follow up on yesterday's challenges
    if yesterday:
        effectiveness, problems = yesterday
        if effectiveness and effectiveness < 3.0 and problems:
            return f"Hi {name}! Yesterday was challenging with {problems}. Let's start today with a fresh approach. What would you like to focus on first?"

    # Priority 3: Regular time-based greetings
    if 6 <= current_hour < 12:
        return f"Good morning, {name}! Ready to tackle your learning goals today? What's your main focus?"
    elif 12 <= current_hour < 18:
        return f"Good afternoon, {name}! How's your learning progress going today?"
    elif 18 <= current_hour < 22:
        return f"Good evening, {name}! Time to reflect on today's learning. What went well?"

    return None


# --- SCRIPT EXECUTION ---

if __name__ == "__main__":
    knowledge_collection = initialize_system()
    main_loop(knowledge_collection)
